// Authentication middleware
const requireAdminAuth = (req, res, next) => {
  if (!req.session.user || req.session.user.role !== 'admin') {
    return res.status(401).json({ 
      error: 'Admin access required',
      code: 'ADMIN_AUTH_REQUIRED'
    });
  }
  next();
};

const requireCustomerAuth = (req, res, next) => {
  if (!req.session.customer) {
    return res.status(401).json({ 
      error: 'Customer authentication required',
      code: 'CUSTOMER_AUTH_REQUIRED'
    });
  }
  next();
};

const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.session.user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }
    
    if (!roles.includes(req.session.user.role)) {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS'
      });
    }
    
    next();
  };
};

module.exports = {
  requireAdminAuth,
  requireCustomerAuth,
  requireRole
};