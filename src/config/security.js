const rateLimit = require('express-rate-limit');

// Recovery phrase word lists for generation
const RECOVERY_WORDS = {
  adjectives: ['quick', 'lazy', 'beautiful', 'angry', 'calm', 'brave', 'clever', 'gentle', 'happy', 'proud'],
  nouns: ['fox', 'dog', 'cat', 'tree', 'river', 'mountain', 'ocean', 'star', 'moon', 'sun'],
  verbs: ['runs', 'jumps', 'sleeps', 'thinks', 'dreams', 'flies', 'swims', 'climbs', 'dances', 'sings']
};

// Turnstile Configuration
const TURNSTILE_SECRET_KEY = process.env.TURNSTILE_SECRET_KEY || '';

// Production-ready auth limiter
const authLimiter = rateLimit({
  windowMs: 30 * 1000, // 30 seconds
  max: 12,
  message: {
    error: "Too many authentication attempts",
    code: "RATE_LIMITED",
    retry_after: 30
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Enhanced login limiter with stricter controls
const enhancedLoginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5,
  message: {
    error: "Too many login attempts from this IP",
    code: "LOGIN_RATE_LIMITED",
    retry_after: 900
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true
});

// Registration limiter
const registrationLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3,
  message: {
    error: "Too many registration attempts from this IP",
    code: "REGISTRATION_RATE_LIMITED",
    retry_after: 3600
  },
  standardHeaders: true,
  legacyHeaders: false
});

module.exports = {
  RECOVERY_WORDS,
  TURNSTILE_SECRET_KEY,
  authLimiter,
  enhancedLoginLimiter,
  registrationLimiter
};