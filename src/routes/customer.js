const express = require('express');
const router = express.Router();
const { requireCustomerAuth } = require('../middleware/authentication');
const { enhancedLoginLimiter, registrationLimiter } = require('../config/security');

// Customer routes - serve React app for SPA routes
router.get('/login', (req, res) => {
  res.sendFile('index.html', { root: './public' });
});

router.get('/register', (req, res) => {
  res.sendFile('index.html', { root: './public' });
});

router.get('/dashboard', (req, res) => {
  res.sendFile('index.html', { root: './public' });
});

router.get('/forgot-password', (req, res) => {
  res.sendFile('index.html', { root: './public' });
});

// API endpoints
router.post('/login', enhancedLoginLimiter, async (req, res) => {
  // TODO: Implement customer login logic
  res.json({ success: false, error: 'Not implemented yet' });
});

router.post('/register', registrationLimiter, async (req, res) => {
  // TODO: Implement customer registration logic
  res.json({ success: false, error: 'Not implemented yet' });
});

// Customer logout
router.get('/logout', (req, res) => {
  req.session.destroy();
  res.redirect('/customer/login');
});

// TODO: Add other customer routes from original backend.js
// - reset HWID
// - downloads
// - generate download token
// - verify recovery
// - reset password

module.exports = router;