const express = require('express');
const router = express.Router();
const { requireAdminAuth } = require('../middleware/authentication');
const { enhancedLoginLimiter } = require('../config/security');

// Admin login page - serve React app
router.get('/login', (req, res) => {
  res.sendFile('index.html', { root: './public' });
});

// Admin login handler
router.post('/login', enhancedLoginLimiter, async (req, res) => {
  // TODO: Implement admin login logic
  // This will be extracted from the original backend.js
  res.json({ success: false, error: 'Not implemented yet' });
});

// Admin dashboard - serve React app
router.get('/dashboard', requireAdminAuth, (req, res) => {
  res.sendFile('index.html', { root: './public' });
});

// Admin logout
router.get('/logout', (req, res) => {
  req.session.destroy();
  res.redirect('/admin/login');
});

// TODO: Add other admin routes from original backend.js
// - bulk actions
// - file upload
// - rate limit status

module.exports = router;