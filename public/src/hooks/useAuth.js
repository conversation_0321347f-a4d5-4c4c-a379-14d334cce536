import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [customer, setCustomer] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const response = await axios.get('/api/auth/status');
      if (response.data.user) {
        setUser(response.data.user);
      }
      if (response.data.customer) {
        setCustomer(response.data.customer);
      }
    } catch (error) {
      console.log('Not authenticated');
    } finally {
      setLoading(false);
    }
  };

  const loginAdmin = async (username, password) => {
    try {
      const response = await axios.post('/admin/login', {
        username,
        password
      });
      
      if (response.data.success) {
        setUser(response.data.user);
        return { success: true };
      }
      
      return { success: false, error: response.data.error };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.error || 'Login failed' 
      };
    }
  };

  const loginCustomer = async (username, password, twoFactorCode) => {
    try {
      const response = await axios.post('/customer/login', {
        username,
        password,
        twoFactorCode
      });
      
      if (response.data.success) {
        setCustomer(response.data.customer);
        return { success: true };
      }
      
      return { success: false, error: response.data.error };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.error || 'Login failed' 
      };
    }
  };

  const logout = async (type = 'admin') => {
    try {
      await axios.get(`/${type}/logout`);
      if (type === 'admin') {
        setUser(null);
      } else {
        setCustomer(null);
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const value = {
    user,
    customer,
    loading,
    loginAdmin,
    loginCustomer,
    logout,
    checkAuthStatus
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};