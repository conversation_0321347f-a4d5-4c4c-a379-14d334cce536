import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';

const AdminDashboard = () => {
  const { user, logout, loading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!loading && !user) {
      navigate('/admin/login');
    }
  }, [user, loading, navigate]);

  const handleLogout = async () => {
    await logout('admin');
    navigate('/admin/login');
  };

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1>Admin Dashboard</h1>
        <p>Welcome back, {user.username}</p>
        <button onClick={handleLogout} className="btn btn-secondary">
          Logout
        </button>
      </div>

      <div className="dashboard-content">
        <div className="card">
          <h3>System Overview</h3>
          <p>Monitor system health and performance metrics.</p>
        </div>

        <div className="card">
          <h3>User Management</h3>
          <p>Manage customer accounts and permissions.</p>
        </div>

        <div className="card">
          <h3>License Management</h3>
          <p>Create and manage software licenses.</p>
        </div>

        <div className="card">
          <h3>Security Monitoring</h3>
          <p>Monitor for security threats and anti-reversing activities.</p>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;