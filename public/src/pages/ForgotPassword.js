import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [recoveryPhrase, setRecoveryPhrase] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [step, setStep] = useState(1);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);

  const handleEmailSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // TODO: Implement email verification
      setStep(2);
      setSuccess('Recovery phrase verified. Please enter your new password.');
    } catch (error) {
      setError('Email not found or invalid.');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordReset = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // TODO: Implement password reset
      setSuccess('Password reset successful! You can now login with your new password.');
      setStep(3);
    } catch (error) {
      setError('Password reset failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (step === 3) {
    return (
      <div className="auth-container">
        <div className="auth-header">
          <h1>Password Reset Successful</h1>
        </div>
        
        <div className="alert alert-success">
          Your password has been reset successfully!
        </div>

        <Link to="/customer/login" className="btn btn-primary">
          Return to Login
        </Link>
      </div>
    );
  }

  return (
    <div className="auth-container">
      <div className="auth-header">
        <h1>Reset Password</h1>
        <p>{step === 1 ? 'Enter your email address' : 'Enter your recovery phrase and new password'}</p>
      </div>

      {error && (
        <div className="alert alert-error">
          {error}
        </div>
      )}

      {success && (
        <div className="alert alert-success">
          {success}
        </div>
      )}

      {step === 1 ? (
        <form onSubmit={handleEmailSubmit}>
          <div className="form-group">
            <label htmlFor="email">Email Address</label>
            <input
              type="email"
              id="email"
              className="form-control"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              disabled={loading}
            />
          </div>

          <button 
            type="submit" 
            className="btn btn-primary"
            disabled={loading}
          >
            {loading ? 'Verifying...' : 'Continue'}
          </button>
        </form>
      ) : (
        <form onSubmit={handlePasswordReset}>
          <div className="form-group">
            <label htmlFor="recoveryPhrase">Recovery Phrase</label>
            <input
              type="text"
              id="recoveryPhrase"
              className="form-control"
              value={recoveryPhrase}
              onChange={(e) => setRecoveryPhrase(e.target.value)}
              placeholder="Enter your recovery phrase"
              required
              disabled={loading}
            />
          </div>

          <div className="form-group">
            <label htmlFor="newPassword">New Password</label>
            <input
              type="password"
              id="newPassword"
              className="form-control"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              required
              disabled={loading}
            />
          </div>

          <button 
            type="submit" 
            className="btn btn-primary"
            disabled={loading}
          >
            {loading ? 'Resetting...' : 'Reset Password'}
          </button>
        </form>
      )}

      <div className="auth-links">
        <Link to="/customer/login">Back to Login</Link>
      </div>
    </div>
  );
};

export default ForgotPassword;