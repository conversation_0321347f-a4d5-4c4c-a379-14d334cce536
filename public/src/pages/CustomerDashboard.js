import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';

const CustomerDashboard = () => {
  const { customer, logout, loading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!loading && !customer) {
      navigate('/customer/login');
    }
  }, [customer, loading, navigate]);

  const handleLogout = async () => {
    await logout('customer');
    navigate('/customer/login');
  };

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  if (!customer) {
    return null;
  }

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1>Customer Dashboard</h1>
        <p>Welcome back, {customer.username}</p>
        <button onClick={handleLogout} className="btn btn-secondary">
          Logout
        </button>
      </div>

      <div className="dashboard-content">
        <div className="card">
          <h3>Your Licenses</h3>
          <p>View and manage your active software licenses.</p>
        </div>

        <div className="card">
          <h3>Downloads</h3>
          <p>Access your licensed software downloads.</p>
        </div>

        <div className="card">
          <h3>Account Settings</h3>
          <p>Update your account information and security settings.</p>
        </div>

        <div className="card">
          <h3>Support</h3>
          <p>Get help and contact customer support.</p>
        </div>
      </div>
    </div>
  );
};

export default CustomerDashboard;