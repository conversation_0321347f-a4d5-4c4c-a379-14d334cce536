# @babel/plugin-bugfix-safari-class-field-initializer-scope

> Wrap class field initializers with IIFE to workaround https://webkit.org/b/236843

See our website [@babel/plugin-bugfix-safari-class-field-initializer-scope](https://babeljs.io/docs/babel-plugin-bugfix-safari-class-field-initializer-scope) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-bugfix-safari-class-field-initializer-scope
```

or using yarn:

```sh
yarn add @babel/plugin-bugfix-safari-class-field-initializer-scope --dev
```
