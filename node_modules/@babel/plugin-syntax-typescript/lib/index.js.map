{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "removePlugin", "plugins", "name", "indices", "for<PERSON>ach", "plugin", "i", "n", "Array", "isArray", "unshift", "splice", "_default", "exports", "default", "declare", "api", "opts", "assertVersion", "disallowAmbiguousJSXLike", "dts", "isTSX", "manipulateOptions", "parserOpts", "push"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\n\nif (!process.env.BABEL_8_BREAKING) {\n  // eslint-disable-next-line no-var\n  var removePlugin = function (plugins: any[], name: string) {\n    const indices: number[] = [];\n    plugins.forEach((plugin, i) => {\n      const n = Array.isArray(plugin) ? plugin[0] : plugin;\n\n      if (n === name) {\n        indices.unshift(i);\n      }\n    });\n\n    for (const i of indices) {\n      plugins.splice(i, 1);\n    }\n  };\n}\n\nexport interface Options {\n  disallowAmbiguousJSXLike?: boolean;\n  dts?: boolean;\n  isTSX?: boolean;\n}\n\nexport default declare((api, opts: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const { disallowAmbiguousJSXLike, dts } = opts;\n\n  if (!process.env.BABEL_8_BREAKING) {\n    // eslint-disable-next-line no-var\n    var { isTSX } = opts;\n  }\n\n  return {\n    name: \"syntax-typescript\",\n\n    manipulateOptions(opts, parserOpts) {\n      if (!process.env.BABEL_8_BREAKING) {\n        const { plugins } = parserOpts;\n        // If the Flow syntax plugin already ran, remove it since Typescript\n        // takes priority.\n        removePlugin(plugins, \"flow\");\n\n        // If the JSX syntax plugin already ran, remove it because JSX handling\n        // in TS depends on the extensions, and is purely dependent on 'isTSX'.\n        removePlugin(plugins, \"jsx\");\n\n        if (!process.env.BABEL_8_BREAKING) {\n          // These are now enabled by default in @babel/parser, but we push\n          // them for compat with older versions.\n          // @ts-ignore(Babel 7 vs Babel 8) These plugins have been removed\n          plugins.push(\"objectRestSpread\", \"classProperties\");\n        }\n\n        if (isTSX) {\n          plugins.push(\"jsx\");\n        }\n      }\n\n      parserOpts.plugins.push([\n        \"typescript\",\n        { disallowAmbiguousJSXLike, dts },\n      ]);\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAEmC;EAEjC,IAAIC,YAAY,GAAG,SAAAA,CAAUC,OAAc,EAAEC,IAAY,EAAE;IACzD,MAAMC,OAAiB,GAAG,EAAE;IAC5BF,OAAO,CAACG,OAAO,CAAC,CAACC,MAAM,EAAEC,CAAC,KAAK;MAC7B,MAAMC,CAAC,GAAGC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM;MAEpD,IAAIE,CAAC,KAAKL,IAAI,EAAE;QACdC,OAAO,CAACO,OAAO,CAACJ,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;IAEF,KAAK,MAAMA,CAAC,IAAIH,OAAO,EAAE;MACvBF,OAAO,CAACU,MAAM,CAACL,CAAC,EAAE,CAAC,CAAC;IACtB;EACF,CAAC;AACH;AAAC,IAAAM,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAQc,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,IAAa,KAAK;EAC7CD,GAAG,CAACE,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAM;IAAEC,wBAAwB;IAAEC;EAAI,CAAC,GAAGH,IAAI;EAEX;IAEjC,IAAI;MAAEI;IAAM,CAAC,GAAGJ,IAAI;EACtB;EAEA,OAAO;IACLf,IAAI,EAAE,mBAAmB;IAEzBoB,iBAAiBA,CAACL,IAAI,EAAEM,UAAU,EAAE;MACC;QACjC,MAAM;UAAEtB;QAAQ,CAAC,GAAGsB,UAAU;QAG9BvB,YAAY,CAACC,OAAO,EAAE,MAAM,CAAC;QAI7BD,YAAY,CAACC,OAAO,EAAE,KAAK,CAAC;QAEO;UAIjCA,OAAO,CAACuB,IAAI,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;QACrD;QAEA,IAAIH,KAAK,EAAE;UACTpB,OAAO,CAACuB,IAAI,CAAC,KAAK,CAAC;QACrB;MACF;MAEAD,UAAU,CAACtB,OAAO,CAACuB,IAAI,CAAC,CACtB,YAAY,EACZ;QAAEL,wBAAwB;QAAEC;MAAI,CAAC,CAClC,CAAC;IACJ;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}